{"queries": [{"id": "6a549430d787912b949d078ba34f73b3", "query": "أعرض لي أكثر 5 منتجات مبيعاً", "sql": "SELECT TOP 5 ItemName, SUM(Quantity) AS TotalQuantity FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' GROUP BY ItemName ORDER BY TotalQuantity DESC;", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363}, {"ItemName": "ميكروويف", "TotalQuantity": 346}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344}, {"ItemName": "ت<PERSON><PERSON> خلاص", "TotalQuantity": 330}, {"ItemName": "مكي<PERSON> هواء", "TotalQuantity": 330}], "analysis": "تحليل البيانات يكشف عن تركز المبيعات بشكل كبير في فئة الإلكترونيات والمنزل. المنتجات الإلكترونية مثل القرص الصلب والميكروويف تتصدر القائمة، بينما المنتجات المنزلية مثل الطاولة الجانبية والمكيف الهوائي تأتي في المراتب الثالثة والخامسة. هذا يدل على أن العملاء يفضلون المنتجات التي تجمع بين الجودة والفعالية في الاستخدام.", "timestamp": "2025-07-25T08:35:52.596Z", "expiresAt": "2025-07-25T09:35:52.596Z", "executionTime": 74798, "dataHash": "bcf3fe7774e73c68ac6e648aab1cb27d"}, {"id": "9678da210b995526fec301c602b830c5", "query": "اكثر منتجين مبيعاً", "sql": "SELECT TOP 2 ItemName, SUM(Quantity) AS TotalQuantity FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' GROUP BY ItemName ORDER BY TotalQuantity DESC;", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363}, {"ItemName": "ميكروويف", "TotalQuantity": 346}], "analysis": "البيانات تكشف عن تفوق واضح لمنتج 'قرص صلب 1TB' على 'ميكروويف' بمقدار 7% في المبيعات. هذا التفوق يمكن أن يعزى إلى زيادة الطلب على التخزين السحابي وتحسينات التقنية في أجهزة الكمبيوتر الشخصية. من الناحية الاستراتيجية، يمكن للشركة الاستفادة من هذه النتائج لتعزيز مبيعات المنتجات ذات الأداء العالي وتطوير استراتيجيات تسويقية موجهة.", "timestamp": "2025-07-25T08:40:05.549Z", "expiresAt": "2025-07-25T09:40:05.549Z", "executionTime": 57391, "dataHash": "f6ad83bffd6c36284a225f2385bf8fb6"}, {"id": "acfab6a3a8a2b677ea14e2f2008eb255", "query": "تفاصيل منتج ميكروويف", "sql": "SELECT ItemID, ItemName, CategoryID, CategoryName, UnitID, UnitName, UnitPrice, Quantity, Amount, StoreID, StoreName, ClientID, ClientName, TheDate, DocumentName, DocumentID, BranchID, BranchName, ISActive, SerialNumber, Barcode, ItemNumber FROM tbltemp_ItemsMain WHERE ItemName = N'ميكروويف';", "data": [{"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "9", "ClientName": "عباس يوسف", "TheDate": "2025-04-23T17:12:55.163Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1002", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000006", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "6", "ClientName": "نورة راشد", "TheDate": "2025-04-23T00:55:01.873Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1003", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000009", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "13", "ClientName": "طارق زايد", "TheDate": "2025-04-23T15:38:46.560Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1004", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000011", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "8", "ClientName": "ريم سلطان", "TheDate": "2025-04-23T06:16:13.163Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1005", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000019", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "1", "ClientName": "<PERSON><PERSON><PERSON><PERSON> محمد", "TheDate": "2025-04-23T16:42:21.020Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1007", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000029", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "1", "ClientName": "<PERSON><PERSON><PERSON><PERSON> محمد", "TheDate": "2025-04-24T05:25:00.480Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1012", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000043", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "4", "ClientName": "فاطمة عمر", "TheDate": "2025-04-25T01:54:57.247Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1026", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000091", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": -2, "Amount": -640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "16", "ClientName": "جمي<PERSON>ة خليل", "TheDate": "2025-04-26T17:39:29.310Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1028", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000098", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "18", "ClientName": "سمر فهد", "TheDate": "2025-04-26T21:16:31.243Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1029", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000099", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "1", "ClientName": "<PERSON><PERSON><PERSON><PERSON> محمد", "TheDate": "2025-04-26T11:44:28.950Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1035", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000114", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "20", "ClientName": "عبير زاهر", "TheDate": "2025-04-26T11:21:24.643Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1036", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000116", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "9", "ClientName": "عباس يوسف", "TheDate": "2025-04-26T23:32:25.787Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1038", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000121", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "2", "ClientName": "سارة خالد", "TheDate": "2025-04-26T10:16:56.043Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1040", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000127", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "17", "ClientName": "بدر سالم", "TheDate": "2025-04-27T04:44:00.320Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1045", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000144", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "1", "ClientName": "<PERSON><PERSON><PERSON><PERSON> محمد", "TheDate": "2025-04-30T14:39:54.280Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1074", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000226", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "10", "ClientName": "منيرة عبد الله", "TheDate": "2025-05-02T07:49:01.183Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1087", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000276", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": -2, "Amount": -640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "5", "ClientName": "ماج<PERSON> سعيد", "TheDate": "2025-05-05T07:10:15.157Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1113", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000350", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "2", "ClientName": "سارة خالد", "TheDate": "2025-05-05T10:41:20.947Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1118", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000365", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "2", "ClientName": "سارة خالد", "TheDate": "2025-05-05T10:41:20.947Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1118", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000367", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "10", "ClientName": "منيرة عبد الله", "TheDate": "2025-05-06T20:35:32.177Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1128", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000404", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "13", "ClientName": "طارق زايد", "TheDate": "2025-05-06T08:05:01.210Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1130", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000415", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "5", "ClientName": "ماج<PERSON> سعيد", "TheDate": "2025-05-07T17:16:19.080Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1137", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000439", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "4", "ClientName": "فاطمة عمر", "TheDate": "2025-05-08T03:44:57.410Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1141", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000446", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "1", "ClientName": "<PERSON><PERSON><PERSON><PERSON> محمد", "TheDate": "2025-05-08T13:57:03.310Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1144", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000457", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "7", "ClientName": "<PERSON><PERSON><PERSON><PERSON> حمد", "TheDate": "2025-05-08T02:16:48.220Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1146", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000463", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "11", "ClientName": "سليمان صالح", "TheDate": "2025-05-09T16:16:06.323Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1155", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000486", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "2", "ClientName": "سارة خالد", "TheDate": "2025-05-10T15:34:31.857Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1156", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000490", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "3", "ClientName": "علي عبدالله", "TheDate": "2025-05-10T05:50:04.553Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1157", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000492", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "15", "ClientName": "فار<PERSON> محمد", "TheDate": "2025-05-10T17:21:03.130Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1160", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000500", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "5", "ClientName": "ماج<PERSON> سعيد", "TheDate": "2025-05-11T23:38:47.337Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1170", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000525", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "7", "ClientName": "<PERSON><PERSON><PERSON><PERSON> حمد", "TheDate": "2025-05-11T06:37:32.697Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1171", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000527", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "8", "ClientName": "ريم سلطان", "TheDate": "2025-05-14T13:22:13.500Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1195", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000595", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "16", "ClientName": "جمي<PERSON>ة خليل", "TheDate": "2025-05-15T17:32:14.900Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1205", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000622", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "16", "ClientName": "جمي<PERSON>ة خليل", "TheDate": "2025-05-17T06:19:24.000Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1221", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000672", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": -3, "Amount": -960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "11", "ClientName": "سليمان صالح", "TheDate": "2025-05-17T17:16:04.100Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1224", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000684", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": -1, "Amount": -320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "11", "ClientName": "سليمان صالح", "TheDate": "2025-05-17T17:16:04.100Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1224", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000685", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "11", "ClientName": "سليمان صالح", "TheDate": "2025-05-17T17:16:04.100Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1224", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000686", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "13", "ClientName": "طارق زايد", "TheDate": "2025-05-18T01:18:18.000Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1229", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000697", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "5", "ClientName": "ماج<PERSON> سعيد", "TheDate": "2025-05-18T01:23:04.297Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1234", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000715", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "18", "ClientName": "سمر فهد", "TheDate": "2025-05-21T12:59:04.903Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1256", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000774", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "2", "ClientName": "سارة خالد", "TheDate": "2025-05-22T01:30:53.533Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1264", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000803", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "18", "ClientName": "سمر فهد", "TheDate": "2025-05-23T11:15:34.990Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1267", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000813", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "17", "ClientName": "بدر سالم", "TheDate": "2025-05-23T18:05:25.373Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1271", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000821", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "2", "ClientName": "سارة خالد", "TheDate": "2025-05-24T15:50:32.913Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1276", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000834", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "10", "ClientName": "منيرة عبد الله", "TheDate": "2025-05-24T19:37:26.793Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1277", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000836", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "4", "ClientName": "فاطمة عمر", "TheDate": "2025-05-24T14:39:17.470Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1278", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000838", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "12", "ClientName": "هدى ناصر", "TheDate": "2025-05-24T12:48:18.020Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1281", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000849", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "20", "ClientName": "عبير زاهر", "TheDate": "2025-05-25T11:55:51.840Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1284", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000856", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "16", "ClientName": "جمي<PERSON>ة خليل", "TheDate": "2025-05-25T19:31:11.167Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1287", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000866", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "10", "ClientName": "منيرة عبد الله", "TheDate": "2025-05-25T07:17:15.453Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1293", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000884", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "17", "ClientName": "بدر سالم", "TheDate": "2025-05-25T23:11:09.930Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1295", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000890", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "16", "ClientName": "جمي<PERSON>ة خليل", "TheDate": "2025-05-26T18:35:02.323Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1298", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000897", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "17", "ClientName": "بدر سالم", "TheDate": "2025-05-26T17:11:21.530Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1305", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000912", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "8", "ClientName": "ريم سلطان", "TheDate": "2025-05-27T20:24:54.890Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1312", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000931", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "19", "ClientName": "نواف رضا", "TheDate": "2025-05-27T18:10:55.360Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1317", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000941", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "9", "ClientName": "عباس يوسف", "TheDate": "2025-05-28T23:11:48.637Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1322", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000953", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "9", "ClientName": "عباس يوسف", "TheDate": "2025-05-28T23:11:48.637Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1322", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000954", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "6", "ClientName": "نورة راشد", "TheDate": "2025-05-29T16:26:17.013Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1333", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN000985", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "3", "ClientName": "علي عبدالله", "TheDate": "2025-06-02T09:12:18.583Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1365", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001083", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "6", "ClientName": "نورة راشد", "TheDate": "2025-06-03T14:41:04.870Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1373", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001106", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "6", "ClientName": "نورة راشد", "TheDate": "2025-06-03T14:41:04.870Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1373", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001108", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "8", "ClientName": "ريم سلطان", "TheDate": "2025-06-03T15:45:45.860Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1376", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001120", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "7", "ClientName": "<PERSON><PERSON><PERSON><PERSON> حمد", "TheDate": "2025-06-04T00:33:55.970Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1392", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001168", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "15", "ClientName": "فار<PERSON> محمد", "TheDate": "2025-06-06T11:54:51.210Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1408", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001222", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "14", "ClientName": "<PERSON><PERSON><PERSON> أحمد", "TheDate": "2025-06-10T18:11:08.140Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1435", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001310", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "2", "ClientName": "سارة خالد", "TheDate": "2025-06-11T09:07:26.117Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1448", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001353", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "2", "ClientName": "سارة خالد", "TheDate": "2025-06-11T09:07:26.117Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1448", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001356", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "5", "ClientName": "ماج<PERSON> سعيد", "TheDate": "2025-06-11T16:15:10.077Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1454", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001378", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "18", "ClientName": "سمر فهد", "TheDate": "2025-06-13T10:54:50.600Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1463", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001399", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": -1, "Amount": -320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "18", "ClientName": "سمر فهد", "TheDate": "2025-06-14T06:46:03.223Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1468", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001411", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "14", "ClientName": "<PERSON><PERSON><PERSON> أحمد", "TheDate": "2025-06-14T17:53:56.230Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1472", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001426", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "9", "ClientName": "عباس يوسف", "TheDate": "2025-06-14T07:28:22.657Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1473", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001429", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "9", "ClientName": "عباس يوسف", "TheDate": "2025-06-15T20:15:17.087Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1479", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001447", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "4", "ClientName": "فاطمة عمر", "TheDate": "2025-06-15T08:17:44.273Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1480", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001449", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "3", "ClientName": "علي عبدالله", "TheDate": "2025-06-17T10:56:53.660Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1495", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001496", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": -3, "Amount": -960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "3", "ClientName": "علي عبدالله", "TheDate": "2025-06-17T10:56:53.660Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1495", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001497", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "18", "ClientName": "سمر فهد", "TheDate": "2025-06-17T21:21:32.120Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1496", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001499", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "13", "ClientName": "طارق زايد", "TheDate": "2025-06-18T03:04:30.163Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1502", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001513", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "10", "ClientName": "منيرة عبد الله", "TheDate": "2025-06-19T01:06:21.813Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1519", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001553", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "14", "ClientName": "<PERSON><PERSON><PERSON> أحمد", "TheDate": "2025-06-20T08:26:24.437Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1525", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001568", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "1", "ClientName": "<PERSON><PERSON><PERSON><PERSON> محمد", "TheDate": "2025-06-21T15:32:28.180Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1534", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001602", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": -1, "Amount": -320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "3", "ClientName": "علي عبدالله", "TheDate": "2025-06-23T15:19:11.030Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1545", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001631", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "5", "ClientName": "ماج<PERSON> سعيد", "TheDate": "2025-06-23T14:47:07.110Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1546", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001634", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "4", "ClientName": "فاطمة عمر", "TheDate": "2025-06-24T05:34:43.933Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1555", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001656", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "15", "ClientName": "فار<PERSON> محمد", "TheDate": "2025-06-24T08:07:47.040Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1559", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001671", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "15", "ClientName": "فار<PERSON> محمد", "TheDate": "2025-06-24T08:07:47.040Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1559", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001672", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "19", "ClientName": "نواف رضا", "TheDate": "2025-06-24T21:13:43.190Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1561", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001678", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "20", "ClientName": "عبير زاهر", "TheDate": "2025-06-24T22:43:58.027Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1562", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001680", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "11", "ClientName": "سليمان صالح", "TheDate": "2025-06-25T19:25:14.197Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1565", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001690", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "20", "ClientName": "عبير زاهر", "TheDate": "2025-06-25T00:44:20.247Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1568", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001697", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "11", "ClientName": "سليمان صالح", "TheDate": "2025-06-28T01:15:49.103Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1586", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001757", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "17", "ClientName": "بدر سالم", "TheDate": "2025-07-01T07:32:17.027Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1610", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001821", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "8", "ClientName": "ريم سلطان", "TheDate": "2025-07-02T21:07:27.293Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1611", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001824", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "8", "ClientName": "ريم سلطان", "TheDate": "2025-07-02T21:07:27.293Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1611", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001826", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "17", "ClientName": "بدر سالم", "TheDate": "2025-07-02T18:11:59.857Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1612", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001827", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "10", "ClientName": "منيرة عبد الله", "TheDate": "2025-07-02T04:41:29.603Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1613", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001832", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "10", "ClientName": "منيرة عبد الله", "TheDate": "2025-07-03T04:50:37.443Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1626", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001873", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "7", "ClientName": "<PERSON><PERSON><PERSON><PERSON> حمد", "TheDate": "2025-07-05T15:02:42.437Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1643", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001925", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "16", "ClientName": "جمي<PERSON>ة خليل", "TheDate": "2025-07-06T23:50:04.933Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1652", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001958", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "18", "ClientName": "سمر فهد", "TheDate": "2025-07-07T02:32:25.820Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1659", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001982", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "12", "ClientName": "هدى ناصر", "TheDate": "2025-07-08T00:53:27.627Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1660", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN001983", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "18", "ClientName": "سمر فهد", "TheDate": "2025-07-09T23:37:54.427Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1667", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002002", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "18", "ClientName": "سمر فهد", "TheDate": "2025-07-09T23:37:54.427Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1667", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002004", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "10", "ClientName": "منيرة عبد الله", "TheDate": "2025-07-09T02:58:22.320Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1669", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002009", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "12", "ClientName": "هدى ناصر", "TheDate": "2025-07-10T21:51:37.203Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1671", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002011", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "5", "ClientName": "ماج<PERSON> سعيد", "TheDate": "2025-07-10T18:39:31.720Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1675", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002023", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "10", "ClientName": "منيرة عبد الله", "TheDate": "2025-07-10T20:14:37.313Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1676", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002025", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "2", "ClientName": "سارة خالد", "TheDate": "2025-07-10T07:41:21.117Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1680", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002039", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "3", "ClientName": "علي عبدالله", "TheDate": "2025-07-11T14:17:37.670Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1689", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002063", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "20", "ClientName": "عبير زاهر", "TheDate": "2025-07-13T08:05:37.133Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1698", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002088", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "16", "ClientName": "جمي<PERSON>ة خليل", "TheDate": "2025-07-13T08:58:37.067Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1700", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002096", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "4", "ClientName": "فاطمة عمر", "TheDate": "2025-07-13T06:44:02.577Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1705", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002113", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "6", "ClientName": "نورة راشد", "TheDate": "2025-07-13T14:34:32.953Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1710", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002130", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "10", "ClientName": "منيرة عبد الله", "TheDate": "2025-07-14T16:33:47.703Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1711", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002131", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "19", "ClientName": "نواف رضا", "TheDate": "2025-07-15T08:12:21.803Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1726", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002187", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "1", "ClientName": "<PERSON><PERSON><PERSON><PERSON> محمد", "TheDate": "2025-07-16T14:25:24.817Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1728", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002192", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "6", "ClientName": "نورة راشد", "TheDate": "2025-07-16T18:01:47.933Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1734", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002207", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "6", "ClientName": "نورة راشد", "TheDate": "2025-07-16T09:19:10.227Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1740", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002222", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "10", "ClientName": "منيرة عبد الله", "TheDate": "2025-07-17T11:33:40.040Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1748", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002246", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "10", "ClientName": "منيرة عبد الله", "TheDate": "2025-07-17T11:33:40.040Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1748", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002247", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "14", "ClientName": "<PERSON><PERSON><PERSON> أحمد", "TheDate": "2025-07-17T06:07:09.257Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1749", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002251", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 3, "Amount": 960, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "14", "ClientName": "<PERSON><PERSON><PERSON> أحمد", "TheDate": "2025-07-18T17:38:55.480Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1757", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002274", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "9", "ClientName": "عباس يوسف", "TheDate": "2025-07-18T05:42:09.040Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1758", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002280", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 2, "Amount": 640, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "7", "ClientName": "<PERSON><PERSON><PERSON><PERSON> حمد", "TheDate": "2025-07-19T04:07:49.917Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1760", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002284", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "14", "ClientName": "<PERSON><PERSON><PERSON> أحمد", "TheDate": "2025-07-20T22:00:33.903Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1769", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002310", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "14", "ClientName": "<PERSON><PERSON><PERSON> أحمد", "TheDate": "2025-07-20T00:22:24.397Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1771", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002317", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "14", "ClientName": "<PERSON><PERSON><PERSON> أحمد", "TheDate": "2025-07-20T00:22:24.397Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1771", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002318", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "12", "ClientName": "هدى ناصر", "TheDate": "2025-07-22T10:57:15.957Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1786", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002358", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 4, "Amount": 1280, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "5", "ClientName": "ماج<PERSON> سعيد", "TheDate": "2025-07-23T01:56:47.947Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1795", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002380", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 5, "Amount": 1600, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "5", "ClientName": "ماج<PERSON> سعيد", "TheDate": "2025-07-23T01:56:47.947Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1795", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002381", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "12", "ClientName": "هدى ناصر", "TheDate": "2025-07-23T01:11:31.200Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1796", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002382", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "19", "ClientName": "نواف رضا", "TheDate": "2025-07-23T12:08:33.160Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1797", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002391", "Barcode": "010002", "ItemNumber": "10"}, {"ItemID": "10", "ItemName": "ميكروويف", "CategoryID": "105", "CategoryName": "أجهزة منزلية", "UnitID": "1", "UnitName": "وحدة", "UnitPrice": 320, "Quantity": 1, "Amount": 320, "StoreID": "2", "StoreName": "فرع الرياض", "ClientID": "10", "ClientName": "منيرة عبد الله", "TheDate": "2025-07-23T08:35:23.863Z", "DocumentName": "فاتورة مبيعات", "DocumentID": "1801", "BranchID": "2", "BranchName": "فرع الرياض", "ISActive": true, "SerialNumber": "SN002403", "Barcode": "010002", "ItemNumber": "10"}], "analysis": "تحليل البيانات أظهر أن الميكروويف هو منتج شائع في فئة الأجهزة المنزلية، مع متوسط سعر وحدة ثابت عند 320 ريال. تم بيع إجمالي 346 وحدة، مما يشير إلى طلب قوي على المنتج. ومع ذلك، هناك بعض الانحرافات مثل المبيعات السلبية التي تحتاج إلى مراجعة. كما أن هناك فرصة لزيادة المبيعات من خلال تحسين التسويق وتقديم عروض خاصة.", "timestamp": "2025-07-25T08:44:30.167Z", "expiresAt": "2025-07-25T09:44:30.167Z", "executionTime": 76952, "dataHash": "2b2d4869985769f42b9b4182a75340f5"}, {"id": "3daf4680247a516dc7e87f81e6cb1928", "query": "اكثر العملاء شراء", "sql": "SELECT ClientID, ClientName, SUM(Amount) AS TotalAmount FROM tbltemp_ItemsMain WHERE YEAR(TheDate) = YEAR(GETDATE()) AND MONTH(TheDate) = MONTH(GETDATE()) GROUP BY ClientID, ClientName ORDER BY TotalAmount DESC", "data": [{"ClientID": "16", "ClientName": "جمي<PERSON>ة خليل", "TotalAmount": 134100}, {"ClientID": "11", "ClientName": "سليمان صالح", "TotalAmount": 126485}, {"ClientID": "1", "ClientName": "<PERSON><PERSON><PERSON><PERSON> محمد", "TotalAmount": 109860}, {"ClientID": "9", "ClientName": "عباس يوسف", "TotalAmount": 79735}, {"ClientID": "10", "ClientName": "منيرة عبد الله", "TotalAmount": 78030}, {"ClientID": "20", "ClientName": "عبير زاهر", "TotalAmount": 73300}, {"ClientID": "18", "ClientName": "سمر فهد", "TotalAmount": 72020}, {"ClientID": "4", "ClientName": "فاطمة عمر", "TotalAmount": 69785}, {"ClientID": "17", "ClientName": "بدر سالم", "TotalAmount": 69380}, {"ClientID": "5", "ClientName": "ماج<PERSON> سعيد", "TotalAmount": 67710}, {"ClientID": "2", "ClientName": "سارة خالد", "TotalAmount": 52130}, {"ClientID": "8", "ClientName": "ريم سلطان", "TotalAmount": 50870}, {"ClientID": "14", "ClientName": "<PERSON><PERSON><PERSON> أحمد", "TotalAmount": 46695}, {"ClientID": "3", "ClientName": "علي عبدالله", "TotalAmount": 35835}, {"ClientID": "6", "ClientName": "نورة راشد", "TotalAmount": 33880}, {"ClientID": "7", "ClientName": "<PERSON><PERSON><PERSON><PERSON> حمد", "TotalAmount": 30465}, {"ClientID": "12", "ClientName": "هدى ناصر", "TotalAmount": 28780}, {"ClientID": "19", "ClientName": "نواف رضا", "TotalAmount": 28755}, {"ClientID": "15", "ClientName": "فار<PERSON> محمد", "TotalAmount": 26340}, {"ClientID": "13", "ClientName": "طارق زايد", "TotalAmount": 18605}], "analysis": "البيانات تظهر أن العملاء الأكثر إنفاقًا لديهم تأثير كبير على الإيرادات الإجمالية للشركة. جميلة خليل هي العميلة الأكثر إنفاقًا بفارق كبير، مما يدل على ولائها القوي للشركة. يمكن للشركة الاستفادة من هذا الولاء عبر برامج الوفاء ومكافآت العملاء البارزين.", "timestamp": "2025-07-25T08:47:41.673Z", "expiresAt": "2025-07-25T09:47:41.673Z", "executionTime": 80234, "dataHash": "977e305aafa7f0992a5aaaf43431cc7a"}], "version": "1.0.0", "lastCleanup": "2025-07-25T08:35:52.595Z"}