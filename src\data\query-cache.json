{"queries": [{"id": "6a549430d787912b949d078ba34f73b3", "query": "أعرض لي أكثر 5 منتجات مبيعاً", "sql": "SELECT TOP 5 ItemName, SUM(Quantity) AS TotalQuantity FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' GROUP BY ItemName ORDER BY TotalQuantity DESC;", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363}, {"ItemName": "ميكروويف", "TotalQuantity": 346}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344}, {"ItemName": "ت<PERSON><PERSON> خلاص", "TotalQuantity": 330}, {"ItemName": "مكي<PERSON> هواء", "TotalQuantity": 330}], "analysis": "المنتجات الأكثر مبيعاً تتنوع بين الإلكترونيات والأثاث والتمور، مما يشير إلى تنوع اهتمامات العملاء. قرص صلب 1TB هو المنتج الأعلى مبيعاً، مما يعكس الطلب المتزايد على التخزين الرقمي. مكيف الهواء وطاولة الجانبية يعكسان اهتمامات العملاء بالراحة المنزلية. التمر خلاص يظهر الاهتمام بالمنتجات الغذائية المحلية.", "timestamp": "2025-07-25T20:38:34.727Z", "expiresAt": "2025-07-25T21:38:34.727Z", "executionTime": 56137, "dataHash": "bcf3fe7774e73c68ac6e648aab1cb27d"}], "version": "1.0.0", "lastCleanup": "2025-07-25T20:27:12.731Z"}