{"databases": [{"id": "bbb5c18ccad9b8da5a0aaae63e58690a", "config": {"server": "localhost", "database": "SalesTempDB", "username": "myuser", "port": "1433"}, "schema": {"tables": [{"name": "tbltemp_Inv_MainInvoice", "description": "هذا الجدول يحتوي على تفاصيل الفواتير الرئيسية، بما في ذلك معلومات حول العناصر المباعة، الأسعار، الكميات، والعملاء. يتم استخدامه لتتبع المعاملات المالية والمخزون.", "columns": [{"name": "ID", "type": "bigint(19,0)", "description": "رقم التسلسلي الفريد لكل سجل في الجدول.", "nullable": false, "isPrimaryKey": true, "isForeignKey": false}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم الوثيقة المرتبطة بالفاتورة.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "RecordID", "type": "bigint(19,0)", "description": "رقم التسلسلي للسجل في الوثيقة المرتبطة.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "InvoiceID", "type": "bigint(19,0)", "description": "رقم التسلسلي للفاتورة.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "DetailsID", "type": "bigint(19,0)", "description": "رقم التسلسلي لتفاصيل الفاتورة.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "TheDate", "type": "datetime", "description": "تاريخ إصدار الفاتورة.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "EnterTime", "type": "datetime", "description": "وقت إدخال الفاتورة في النظام.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ItemID", "type": "bigint(19,0)", "description": "رقم التسلسلي للعنصر المباع.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "UnitID", "type": "bigint(19,0)", "description": "رقم التسلسلي للوحدة المستخدمة لقياس العنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "UnitPrice", "type": "numeric(18,6)", "description": "سعر الوحدة للعنصر المباع.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "Quantity", "type": "numeric(18,6)", "description": "كمية العنصر المباع.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "TotalAmount", "type": "numeric(18,6)", "description": "المبلغ الإجمالي للفاتورة.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "MainUnitQuantity", "type": "numeric(37,12)", "description": "كمية العنصر بالوحدة الرئيسية.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "MainUnitPrice", "type": "numeric(38,20)", "description": "سعر الوحدة الرئيسية للعنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "StoreID", "type": "bigint(19,0)", "description": "رقم التسلسلي للمخزن الذي تم فيه إجراء المعاملة.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "BranchID", "type": "bigint(19,0)", "description": "رقم التسلسلي للفرع الذي تم فيه إجراء المعاملة.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ExchangeFactor", "type": "numeric(18,6)", "description": "عامل التحويل بين الوحدات المختلفة للعنصر.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ClientID", "type": "bigint(19,0)", "description": "رقم التسلسلي للعميل الذي تم إصدار الفاتورة له.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "MCAmount", "type": "decimal(38,13)", "description": "المبلغ الإجمالي بالعملة الرئيسية.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "NewSubItemEntryID", "type": "bigint(19,0)", "description": "رقم التسلسلي للعنصر الفرعي الجديد المدخل في النظام.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "CurrencyID", "type": "bigint(19,0)", "description": "رقم التسلسلي للعملة المستخدمة في الفاتورة.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>(150)", "description": "طريقة الدفع المستخدمة في الفاتورة.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ExchangePrice", "type": "numeric(18,6)", "description": "سعر الصرف بين العملات المختلفة.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "TotalAmountByCurrencyInvetory", "type": "numeric(38,13)", "description": "المبلغ الإجمالي للفاتورة بالعملة المخزونية.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}]}, {"name": "tbltemp_ItemsMain", "description": "هذا الجدول يحتوي على تفاصيل العناصر الرئيسية، بما في ذلك معلومات حول العملاء، الوثائق، الفروع، والمخازن. يتم استخدامه لتتبع العناصر والمعاملات المرتبطة بها.", "columns": [{"name": "ID", "type": "bigint(19,0)", "description": "رقم التسلسلي الفريد لكل سجل في الجدول.", "nullable": false, "isPrimaryKey": true, "isForeignKey": false}, {"name": "ParentID", "type": "bigint(19,0)", "description": "رقم التسلسلي للعنصر الأصلي إذا كان هذا العنصر فرعيًا.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "DocumentID", "type": "bigint(19,0)", "description": "رقم التسلسلي للوثيقة المرتبطة بالعنصر.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "RecordNumber", "type": "bigint(19,0)", "description": "رقم السجل في الوثيقة المرتبطة.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "RecordID", "type": "bigint(19,0)", "description": "رقم التسلسلي للسجل في الوثيقة المرتبطة.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "TheDate", "type": "datetime", "description": "تاريخ إدخال العنصر في النظام.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ClientID", "type": "bigint(19,0)", "description": "رقم التسلسلي للعميل المرتبط بالعنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ClientName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم العميل المرتبط بالعنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "CurrencyID", "type": "bigint(19,0)", "description": "رقم التسلسلي للعملة المستخدمة في العنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم العملة المستخدمة في العنصر.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "TheMethodID", "type": "bigint(19,0)", "description": "رقم التسلسلي لطريقة الدفع المستخدمة في العنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>(150)", "description": "طريقة الدفع المستخدمة في العنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "UserID", "type": "bigint(19,0)", "description": "رقم التسلسلي للمستخدم الذي أدخل العنصر في النظام.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "BranchID", "type": "bigint(19,0)", "description": "رقم التسلسلي للفرع الذي تم فيه إجراء المعاملة.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "BranchName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم الفرع الذي تم فيه إجراء المعاملة.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم الوثيقة المرتبطة بالعنصر.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ItemID", "type": "bigint(19,0)", "description": "رقم التسلسلي للعنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ItemName", "type": "<PERSON><PERSON><PERSON>(200)", "description": "اسم العنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "CategoryID", "type": "bigint(19,0)", "description": "رقم التسلسلي لفئة العنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "CategoryName", "type": "<PERSON><PERSON><PERSON>(200)", "description": "اسم فئة العنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ItemTypeID", "type": "bigint(19,0)", "description": "رقم التسلسلي لنوع العنصر.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ItemType", "type": "<PERSON><PERSON><PERSON>(150)", "description": "نوع العنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ISActive", "type": "bit", "description": "حالة العنصر (نشطة أو غير نشطة).", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "UnitID", "type": "bigint(19,0)", "description": "رقم التسلسلي للوحدة المستخدمة لقياس العنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "UnitName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم الوحدة المستخدمة لقياس العنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ExchangeFactor", "type": "numeric(18,6)", "description": "عامل التحويل بين الوحدات المختلفة للعنصر.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "UnitPrice", "type": "numeric(18,6)", "description": "سعر الوحدة للعنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "Quantity", "type": "numeric(18,6)", "description": "كمية العنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "Amount", "type": "numeric(18,6)", "description": "المبلغ الإجمالي للعنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "MCAmount", "type": "numeric(18,6)", "description": "المبلغ الإجمالي للعنصر بالعملة الرئيسية.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "StoreID", "type": "bigint(19,0)", "description": "رقم التسلسلي للمخزن الذي تم فيه إجراء المعاملة.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "StoreName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم المخزن الذي تم فيه إجراء المعاملة.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ExchangePrice", "type": "numeric(18,6)", "description": "سعر الصرف بين العملات المختلفة.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ExchangePriceCurrencyInvetory", "type": "decimal(18,6)", "description": "سعر الصرف بين العملات المختلفة بالمخزون.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "UserName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم المستخدم الذي أدخل العنصر في النظام.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}, {"name": "TheYear", "type": "int(10,0)", "description": "السنة التي تم فيها إجراء المعاملة.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "Notes", "type": "<PERSON><PERSON><PERSON>(255)", "description": "ملاحظات إضافية حول العنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "SerialNumber", "type": "<PERSON><PERSON><PERSON>(150)", "description": "رقم التسلسلي للعنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "Barcode", "type": "<PERSON><PERSON><PERSON>(150)", "description": "الباركود الخاص بالعنصر.", "nullable": true, "isPrimaryKey": false, "isForeignKey": false}, {"name": "ItemNumber", "type": "bigint(19,0)", "description": "رقم العنصر في النظام.", "nullable": false, "isPrimaryKey": false, "isForeignKey": false}]}], "intelligentClassification": {"المنتجات": {"description": "تشمل معلومات عن الأصناف، الكمية، السعر، والوحدة.", "columns": ["tbltemp_Inv_MainInvoice.ItemID", "tbltemp_Inv_MainInvoice.UnitID", "tbltemp_Inv_MainInvoice.UnitPrice", "tbltemp_Inv_MainInvoice.Quantity", "tbltemp_Inv_MainInvoice.MainUnitQuantity", "tbltemp_Inv_MainInvoice.MainUnitPrice", "tbltemp_ItemsMain.ItemID", "tbltemp_ItemsMain.ItemName", "tbltemp_ItemsMain.CategoryID", "tbltemp_ItemsMain.CategoryName", "tbltemp_ItemsMain.ItemTypeID", "tbltemp_ItemsMain.ItemType", "tbltemp_ItemsMain.UnitID", "tbltemp_ItemsMain.UnitName", "tbltemp_ItemsMain.UnitPrice", "tbltemp_ItemsMain.Quantity", "tbltemp_ItemsMain.Amount", "tbltemp_ItemsMain.MCAmount", "tbltemp_ItemsMain.ExchangeFactor", "tbltemp_ItemsMain.ExchangePrice", "tbltemp_ItemsMain.ExchangePriceCurrencyInvetory", "tbltemp_ItemsMain.SerialNumber", "tbltemp_ItemsMain.Barcode", "tbltemp_ItemsMain.ItemNumber"], "tables": ["tbltemp_Inv_MainInvoice", "tbltemp_ItemsMain"]}, "العملاء": {"description": "تشمل معلومات عن هوية العميل، الموزع، وسجل الشراء.", "columns": ["tbltemp_Inv_MainInvoice.ClientID", "tbltemp_ItemsMain.ClientID", "tbltemp_ItemsMain.ClientName"], "tables": ["tbltemp_Inv_MainInvoice", "tbltemp_ItemsMain"]}, "المخازن": {"description": "تشمل معلومات عن المستودعات، الفروع، والمخزون.", "columns": ["tbltemp_Inv_MainInvoice.StoreID", "tbltemp_Inv_MainInvoice.BranchID", "tbltemp_ItemsMain.StoreID", "tbltemp_ItemsMain.StoreName", "tbltemp_ItemsMain.BranchID", "tbltemp_ItemsMain.BranchName"], "tables": ["tbltemp_Inv_MainInvoice", "tbltemp_ItemsMain"]}, "المبالغ": {"description": "تشمل معلومات عن القيمة، السعر، والخصومات.", "columns": ["tbltemp_Inv_MainInvoice.UnitPrice", "tbltemp_Inv_MainInvoice.TotalAmount", "tbltemp_Inv_MainInvoice.MCAmount", "tbltemp_Inv_MainInvoice.TotalAmountByCurrencyInvetory", "tbltemp_ItemsMain.UnitPrice", "tbltemp_ItemsMain.Amount", "tbltemp_ItemsMain.MCAmount", "tbltemp_ItemsMain.ExchangePrice", "tbltemp_ItemsMain.ExchangePriceCurrencyInvetory"], "tables": ["tbltemp_Inv_MainInvoice", "tbltemp_ItemsMain"]}, "التواريخ": {"description": "تشمل معلومات عن تواريخ إصدار الفواتير وإدخال العناصر في النظام.", "columns": ["tbltemp_Inv_MainInvoice.TheDate", "tbltemp_Inv_MainInvoice.EnterTime", "tbltemp_ItemsMain.TheDate"], "tables": ["tbltemp_Inv_MainInvoice", "tbltemp_ItemsMain"]}, "الفواتير": {"description": "تشمل معلومات عن المستندات والفواتير والرقم التسلسلي.", "columns": ["tbltemp_Inv_MainInvoice.InvoiceID", "tbltemp_Inv_MainInvoice.DetailsID", "tbltemp_Inv_MainInvoice.DocumentName", "tbltemp_Inv_MainInvoice.RecordID", "tbltemp_ItemsMain.DocumentID", "tbltemp_ItemsMain.RecordNumber", "tbltemp_ItemsMain.RecordID", "tbltemp_ItemsMain.DocumentName"], "tables": ["tbltemp_Inv_MainInvoice", "tbltemp_ItemsMain"]}, "المحاسبة": {"description": "تشمل معلومات عن الحسابات ومراكز التكلفة.", "columns": ["tbltemp_Inv_MainInvoice.CurrencyID", "tbltemp_Inv_MainInvoice.MCAmount", "tbltemp_ItemsMain.CurrencyID", "tbltemp_ItemsMain.CurrencyName", "tbltemp_ItemsMain.MCAmount", "tbltemp_ItemsMain.ExchangePrice", "tbltemp_ItemsMain.ExchangePriceCurrencyInvetory"], "tables": ["tbltemp_Inv_MainInvoice", "tbltemp_ItemsMain"]}}}, "indexed_at": "2025-07-25T00:46:27.998Z", "last_accessed": "2025-07-25T22:22:19.192Z", "version": "1.0.0"}], "version": "1.0.0"}