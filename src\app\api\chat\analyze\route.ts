import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENROUTER_API_KEY || '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',
  baseURL: 'https://openrouter.ai/api/v1',
});

const MODEL_NAME = 'deepseek/deepseek-r1-0528:free';

export async function POST(request: NextRequest) {
  try {
    const { question, queryContext } = await request.json();

    console.log('🔍 تحليل سؤال الدردشة:', question);
    console.log('📊 سياق البيانات:', queryContext?.results?.length || 0, 'عنصر');

    if (!question || !queryContext) {
      return NextResponse.json(
        { error: 'السؤال وسياق الاستعلام مطلوبان' },
        { status: 400 }
      );
    }

    // تحديد نوع السؤال
    const isSimpleAnalysis = checkIfSimpleAnalysis(question);
    console.log('⚡ نوع التحليل:', isSimpleAnalysis ? 'فوري' : 'متقدم');

    if (isSimpleAnalysis) {
      // تحليل فوري للبيانات الحالية
      console.log('⚡ تنفيذ تحليل فوري...');
      const response = await analyzeCurrentData(question, queryContext);
      console.log('✅ تم التحليل الفوري بنجاح');

      return NextResponse.json({
        success: true,
        response,
        type: 'instant_analysis',
        timestamp: Date.now()
      });
    } else {
      // تحليل متقدم - نحاول التحليل الفوري أولاً كـ fallback
      console.log('🧠 محاولة تحليل متقدم...');
      try {
        const response = await generateAdvancedAnalysis(question, queryContext);
        console.log('✅ تم التحليل المتقدم بنجاح');

        return NextResponse.json({
          success: true,
          response,
          type: 'ai_analysis',
          timestamp: Date.now()
        });
      } catch (aiError) {
        console.log('⚠️ فشل التحليل المتقدم، التبديل للتحليل الفوري...');
        // fallback للتحليل الفوري
        const response = await analyzeCurrentData(question, queryContext);

        return NextResponse.json({
          success: true,
          response: response + '\n\n⚠️ تم استخدام التحليل السريع بدلاً من التحليل المتقدم.',
          type: 'fallback_analysis',
          timestamp: Date.now()
        });
      }
    }

  } catch (error) {
    console.error('خطأ في تحليل الدردشة:', error);
    return NextResponse.json(
      { error: 'فشل في تحليل السؤال' },
      { status: 500 }
    );
  }
}

function checkIfSimpleAnalysis(question: string): boolean {
  const simpleKeywords = [
    'لماذا', 'ليش', 'ما السبب', 'قارن', 'الفرق', 'أيهما أفضل', 'النسبة',
    'كم', 'أكثر', 'أقل', 'الأول', 'الثاني', 'الثالث', 'عدد', 'مجموع',
    'المنتج الأول', 'المنتج الثاني', 'العميل الأول', 'الفرع الأول'
  ];

  const questionLower = question.toLowerCase();

  // التحقق من الكلمات المفتاحية
  const hasSimpleKeyword = simpleKeywords.some(keyword =>
    questionLower.includes(keyword.toLowerCase())
  );

  // التحقق من الأسئلة التي تتطلب استعلام جديد
  const needsNewQuery = [
    'الشهر الماضي', 'العام الماضي', 'الأسبوع الماضي', 'أمس',
    'مقارنة زمنية', 'تاريخ', 'فترة', 'خلال', 'منذ'
  ].some(keyword => questionLower.includes(keyword));

  // إذا كان يحتاج استعلام جديد، فهو ليس تحليل بسيط
  if (needsNewQuery) {
    return false;
  }

  return hasSimpleKeyword;
}

async function analyzeCurrentData(question: string, context: any): Promise<string> {
  const data = context.results;
  
  if (!data || data.length === 0) {
    return 'لا توجد بيانات للتحليل.';
  }

  // تحليل "لماذا الأول" أو "لماذا المنتج الأول"
  if ((question.includes('لماذا') || question.includes('ليش')) &&
      (question.includes('الأول') || question.includes('أول') || question.includes('المنتج الأول'))) {

    if (data.length === 0) {
      return 'لا توجد بيانات للتحليل.';
    }

    const firstItem = data[0];
    const keys = Object.keys(firstItem);

    // العثور على اسم المنتج
    const productName = firstItem.ItemName || firstItem.Name || firstItem[keys[0]] || 'المنتج الأول';

    let analysis = `🏆 **${productName}** يتصدر القائمة للأسباب التالية:\n\n`;

    // تحليل تفصيلي للبيانات
    keys.forEach(key => {
      const value = firstItem[key];
      if (typeof value === 'number') {
        if (key.toLowerCase().includes('quantity') || key.includes('كمية')) {
          analysis += `📦 **الكمية المباعة**: ${value.toLocaleString()} وحدة\n`;
        } else if (key.toLowerCase().includes('amount') || key.toLowerCase().includes('revenue') || key.includes('مبلغ') || key.includes('إيراد')) {
          analysis += `💰 **الإيرادات**: ${value.toLocaleString()} ريال\n`;
        } else if (key.toLowerCase().includes('price') || key.includes('سعر')) {
          analysis += `💵 **السعر**: ${value.toLocaleString()} ريال\n`;
        } else {
          analysis += `📊 **${key}**: ${value.toLocaleString()}\n`;
        }
      } else if (value && value !== productName) {
        analysis += `ℹ️ **${key}**: ${value}\n`;
      }
    });

    // مقارنة مع المنتج الثاني إذا وجد
    if (data.length > 1) {
      const secondItem = data[1];
      const secondProductName = secondItem.ItemName || secondItem.Name || secondItem[keys[0]] || 'المنتج الثاني';

      analysis += `\n🔍 **مقارنة مع ${secondProductName}**:\n`;

      keys.forEach(key => {
        const firstValue = firstItem[key];
        const secondValue = secondItem[key];

        if (typeof firstValue === 'number' && typeof secondValue === 'number' && secondValue > 0) {
          const diff = firstValue - secondValue;
          const percentage = ((Math.abs(diff) / secondValue) * 100).toFixed(1);

          if (key.toLowerCase().includes('quantity') || key.includes('كمية')) {
            analysis += `📦 الكمية: أعلى بـ ${diff.toLocaleString()} وحدة (${percentage}%)\n`;
          } else if (key.toLowerCase().includes('amount') || key.toLowerCase().includes('revenue')) {
            analysis += `💰 الإيرادات: أعلى بـ ${diff.toLocaleString()} ريال (${percentage}%)\n`;
          } else if (diff !== 0) {
            analysis += `📊 ${key}: ${diff > 0 ? 'أعلى' : 'أقل'} بـ ${Math.abs(diff).toLocaleString()} (${percentage}%)\n`;
          }
        }
      });
    }

    // إضافة تحليل الأسباب
    analysis += `\n💡 **الأسباب المحتملة للتفوق**:\n`;
    analysis += `• توازن ممتاز بين السعر والجودة\n`;
    analysis += `• طلب عالي من العملاء\n`;
    analysis += `• استراتيجية تسويق فعالة\n`;
    analysis += `• توفر المنتج في الأوقات المناسبة\n`;

    return analysis;
  }

  // تحليل المقارنة
  if (question.includes('قارن') && (question.includes('الأول') || question.includes('الثاني'))) {
    if (data.length >= 2) {
      const first = data[0];
      const second = data[1];
      const keys = Object.keys(first);
      
      let comparison = 'مقارنة تفصيلية:\n\n';
      
      keys.forEach(key => {
        comparison += `📊 ${key}:\n`;
        comparison += `   • الأول: ${typeof first[key] === 'number' ? first[key].toLocaleString() : first[key]}\n`;
        comparison += `   • الثاني: ${typeof second[key] === 'number' ? second[key].toLocaleString() : second[key]}\n`;
        
        if (typeof first[key] === 'number' && typeof second[key] === 'number') {
          const diff = first[key] - second[key];
          const percentage = ((Math.abs(diff) / second[key]) * 100).toFixed(1);
          comparison += `   • الفرق: ${diff > 0 ? '+' : ''}${diff.toLocaleString()} (${percentage}%)\n`;
        }
        comparison += '\n';
      });
      
      return comparison;
    }
  }

  // تحليل العدد والإحصائيات
  if (question.includes('كم') || question.includes('عدد')) {
    let stats = `📈 إحصائيات البيانات:\n\n`;
    stats += `• إجمالي العناصر: ${data.length}\n`;
    
    // حساب إحصائيات للأعمدة الرقمية
    const firstItem = data[0];
    const numericKeys = Object.keys(firstItem).filter(key => typeof firstItem[key] === 'number');
    
    numericKeys.forEach(key => {
      const values = data.map((item: any) => item[key]).filter((val: any) => typeof val === 'number');
      if (values.length > 0) {
        const sum = values.reduce((a: number, b: number) => a + b, 0);
        const avg = sum / values.length;
        const max = Math.max(...values);
        const min = Math.min(...values);
        
        stats += `\n📊 ${key}:\n`;
        stats += `   • المجموع: ${sum.toLocaleString()}\n`;
        stats += `   • المتوسط: ${avg.toLocaleString()}\n`;
        stats += `   • الأعلى: ${max.toLocaleString()}\n`;
        stats += `   • الأقل: ${min.toLocaleString()}\n`;
      }
    });
    
    return stats;
  }

  // تحليل عام
  return `📋 تحليل سريع للبيانات:

• عدد العناصر: ${data.length}
• الاستعلام الأصلي: ${context.query}
• وقت التنفيذ: ${new Date(context.timestamp).toLocaleString('ar')}

يمكنك أن تسأل أسئلة أكثر تحديداً مثل:
• "لماذا العنصر الأول هو الأفضل؟"
• "قارن بين الأول والثاني"
• "كم إجمالي المبيعات؟"`;
}

async function generateAdvancedAnalysis(question: string, context: any): Promise<string> {
  try {
    const prompt = `أنت محلل بيانات خبير. لديك النتائج التالية من استعلام قاعدة البيانات:

الاستعلام الأصلي: ${context.query}
عدد النتائج: ${context.results.length}

عينة من البيانات (أول 5 صفوف):
${JSON.stringify(context.results.slice(0, 5), null, 2)}

السؤال من المستخدم: ${question}

يرجى تقديم تحليل مفصل وعملي باللغة العربية. اجعل الإجابة:
- واضحة ومفهومة
- مبنية على البيانات الفعلية
- تحتوي على رؤى عملية
- مفيدة لاتخاذ القرارات

لا تتجاوز 300 كلمة.`;

    const response = await openai.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: 'أنت محلل بيانات خبير تقدم تحليلات واضحة ومفيدة باللغة العربية.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 500
    });

    return response.choices[0]?.message?.content || 'عذراً، لم أتمكن من تحليل السؤال.';

  } catch (error) {
    console.error('خطأ في التحليل المتقدم:', error);
    return 'عذراً، حدث خطأ أثناء التحليل المتقدم. يرجى المحاولة مرة أخرى.';
  }
}
